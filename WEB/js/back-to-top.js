document.addEventListener('DOMContentLoaded', function() {
    const backToTopBtn = document.getElementById('backToTopBtn');
    let lastScrollTop = 0;
    let ticking = false;

    function updateBackToTopButton(scrollTop) {
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        if (scrollTop > windowHeight / 2) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }

        lastScrollTop = scrollTop;
    }

    function onScroll() {
        if (!ticking) {
            window.requestAnimationFrame(function() {
                updateBackToTopButton(window.pageYOffset);
                ticking = false;
            });

            ticking = true;
        }
    }

    window.addEventListener('scroll', onScroll);

    backToTopBtn.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    updateBackToTopButton(window.pageYOffset);
});